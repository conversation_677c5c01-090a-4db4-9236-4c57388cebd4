from typing import List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import Task, AccountSubmission, SystemNotice
from ..schemas import SharedTaskInfo, AccountSubmissionCreate, SystemNoticeResponse

router = APIRouter(prefix="/api/share", tags=["分享页面"])


@router.get("/{share_token}", response_model=SharedTaskInfo)
def get_shared_task(share_token: str, db: Session = Depends(get_db)):
    """获取分享任务信息"""
    task = db.query(Task).filter(Task.share_token == share_token).first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务是否过期
    if task.expires_at and task.expires_at < datetime.utcnow():
        raise HTTPException(status_code=410, detail="Task has expired")

    # 检查是否已经有人提交过账号信息
    existing_submission = db.query(AccountSubmission).filter(
        AccountSubmission.task_id == task.id
    ).first()

    return SharedTaskInfo(
        id=task.id,
        title=task.title,
        description=task.description,
        task_type=task.task_type,
        duration_days=task.duration_days,
        created_at=task.created_at,
        is_submitted=existing_submission is not None
    )


@router.post("/{share_token}/submit")
def submit_account_info(
    share_token: str,
    account_info: AccountSubmissionCreate,
    db: Session = Depends(get_db)
):
    """提交账号信息"""
    task = db.query(Task).filter(Task.share_token == share_token).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 检查任务是否过期
    if task.expires_at and task.expires_at < datetime.utcnow():
        raise HTTPException(status_code=410, detail="Task has expired")
    
    # 检查任务状态
    if task.status != "待接单":
        raise HTTPException(status_code=400, detail="Task is not available for submission")
    
    # 检查是否已经提交过
    existing_submission = db.query(AccountSubmission).filter(
        AccountSubmission.task_id == task.id
    ).first()
    
    if existing_submission:
        raise HTTPException(status_code=400, detail="Account information already submitted for this task")
    
    # 创建账号提交记录
    submission = AccountSubmission(
        task_id=task.id,
        username=account_info.username,
        password=account_info.password,
        email=account_info.email,
        level_info=account_info.level_info,
        key_info=account_info.key_info,
        need_notification=account_info.need_notification
    )
    
    # 更新任务状态
    task.status = "进行中"
    
    db.add(submission)
    db.commit()
    
    return {"message": "Account information submitted successfully"}


@router.get("/{share_token}/notices", response_model=List[SystemNoticeResponse])
def get_notices_for_share(share_token: str, db: Session = Depends(get_db)):
    """获取分享页面的系统提示信息"""
    # 验证分享令牌是否有效
    task = db.query(Task).filter(Task.share_token == share_token).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 获取活跃的系统提示信息
    notices = db.query(SystemNotice).filter(SystemNotice.is_active == True).all()
    return notices
