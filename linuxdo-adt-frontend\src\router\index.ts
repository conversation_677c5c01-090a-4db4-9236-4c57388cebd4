import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import TaskList from '../views/TaskList.vue'
import AccountList from '../views/AccountList.vue'
import NoticeList from '../views/NoticeList.vue'
import SharePage from '../views/SharePage.vue'
import SystemSettings from '../views/SystemSettings.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/tasks'
      },
      {
        path: '/tasks',
        name: 'TaskList',
        component: TaskList
      },
      {
        path: '/accounts',
        name: 'AccountList',
        component: AccountList
      },
      {
        path: '/notices',
        name: 'NoticeList',
        component: NoticeList,
        meta: { requiresAdmin: true }
      },
      {
        path: '/settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { requiresAdmin: true }
      }
    ]
  },
  {
    path: '/share/:shareToken',
    name: 'SharePage',
    component: SharePage
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('access_token')
  
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else {
    next()
  }
})

export default router
