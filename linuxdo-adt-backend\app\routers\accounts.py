from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, AccountSubmission, Task
from ..schemas import AccountSubmissionResponse, AccountSubmissionCreate
from ..auth import get_current_active_user
from ..crypto_utils import encrypt_password, decrypt_password, get_master_password_from_settings

router = APIRouter(prefix="/api/accounts", tags=["账号管理"])


def decrypt_submission_password(submission: AccountSubmission, db: Session) -> AccountSubmission:
    """解密账号提交记录中的密码"""
    master_password = get_master_password_from_settings(db)
    if master_password:
        submission.password = decrypt_password(submission.password, master_password)
    return submission


def decrypt_submissions_passwords(submissions: List[AccountSubmission], db: Session) -> List[AccountSubmission]:
    """批量解密账号提交记录中的密码"""
    master_password = get_master_password_from_settings(db)
    if master_password:
        for submission in submissions:
            submission.password = decrypt_password(submission.password, master_password)
    return submissions


@router.get("/", response_model=List[AccountSubmissionResponse])
def get_account_submissions(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取当前用户任务的账号提交列表"""
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()

    # 获取这些任务的账号提交记录
    submissions = db.query(AccountSubmission).filter(
        AccountSubmission.task_id.in_(user_task_ids)
    ).offset(skip).limit(limit).all()

    # 解密密码后返回
    return decrypt_submissions_passwords(submissions, db)


@router.get("/{account_id}", response_model=AccountSubmissionResponse)
def get_account_submission(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取账号提交详情"""
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()

    submission = db.query(AccountSubmission).filter(
        AccountSubmission.id == account_id,
        AccountSubmission.task_id.in_(user_task_ids)
    ).first()

    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")

    # 解密密码后返回
    return decrypt_submission_password(submission, db)


@router.put("/{account_id}", response_model=AccountSubmissionResponse)
def update_account_submission(
    account_id: int,
    account_update: AccountSubmissionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新账号提交信息"""
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()

    submission = db.query(AccountSubmission).filter(
        AccountSubmission.id == account_id,
        AccountSubmission.task_id.in_(user_task_ids)
    ).first()

    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")






    master_password = get_master_password_from_settings(db)
    encrypted_password = encrypt_password(account_update.password, master_password)

    # 更新字段
    submission.username = account_update.username
    submission.password = encrypted_password  # 存储加密后的密码
    submission.email = account_update.email
    submission.level_info = account_update.level_info
    submission.key_info = account_update.key_info
    submission.need_notification = account_update.need_notification

    # 同时更新对应任务的标题
    task = db.query(Task).filter(Task.id == submission.task_id).first()
    if task:
        new_title = f"{account_update.username}+{task.task_type}"
        task.title = new_title

    db.commit()
    db.refresh(submission)

    # 解密密码后返回
    return decrypt_submission_password(submission, db)


@router.put("/{account_id}/status")
def update_account_status(
    account_id: int,
    status: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新账号状态"""
    # 验证状态值
    valid_statuses = ["已提交", "处理中", "已完成"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    # 获取当前用户的所有任务ID
    user_task_ids = db.query(Task.id).filter(Task.creator_id == current_user.id).subquery()
    
    submission = db.query(AccountSubmission).filter(
        AccountSubmission.id == account_id,
        AccountSubmission.task_id.in_(user_task_ids)
    ).first()
    
    if not submission:
        raise HTTPException(status_code=404, detail="Account submission not found")
    
    submission.status = status
    
    # 如果账号处理完成，更新对应任务状态
    if status == "已完成":
        task = db.query(Task).filter(Task.id == submission.task_id).first()
        if task:
            task.status = "已完成"
    
    db.commit()
    
    return {"message": "Account status updated successfully"}
