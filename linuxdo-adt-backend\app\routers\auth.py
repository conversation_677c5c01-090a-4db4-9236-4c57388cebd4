from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>F<PERSON>
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import User, SystemSettings
from ..schemas import UserCreate, UserResponse, Token
from ..auth import verify_password, get_password_hash, create_access_token
from ..config import settings
from ..email_service import email_service

router = APIRouter(prefix="/api/auth", tags=["认证"])


@router.post("/register", response_model=UserResponse)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    # 检查注册开关
    registration_setting = db.query(SystemSettings).filter(
        SystemSettings.key == "registration_enabled"
    ).first()

    if registration_setting and registration_setting.value.lower() != "true":
        raise HTTPException(
            status_code=403,
            detail="Registration is currently disabled"
        )

    # 检查用户名是否已存在
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Username already registered"
        )

    # 检查邮箱是否已存在
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )

    # 创建新用户
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        password_hash=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # 发送注册欢迎邮件
    try:
        await email_service.send_registration_notification(
            user_email=db_user.email,
            username=db_user.username,
            db=db
        )
    except Exception as e:
        # 邮件发送失败不影响注册流程
        print(f"发送注册邮件失败: {str(e)}")

    return db_user


@router.post("/login", response_model=Token)
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """用户登录"""
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}
