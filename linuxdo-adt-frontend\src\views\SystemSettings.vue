<template>
  <div class="system-settings">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础设置 -->
        <el-tab-pane label="基础设置" name="basic">
          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="允许注册">
              <el-switch
                v-model="basicSettings.registration_enabled"
                @change="markSettingsChanged"
              />
              <div class="setting-desc">关闭后新用户无法注册账号</div>
            </el-form-item>

            <el-form-item label="网站名称">
              <el-input
                v-model="basicSettings.site_name"
                @input="markSettingsChanged"
                placeholder="请输入网站名称"
              />
            </el-form-item>

            <el-form-item label="网站URL">
              <el-input
                v-model="basicSettings.site_url"
                @input="markSettingsChanged"
                placeholder="请输入网站URL"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="saveBasicSettings"
                :disabled="!basicSettingsChanged"
                :loading="savingBasic"
              >
                保存基础设置
              </el-button>
              <el-button @click="resetBasicSettings" :disabled="!basicSettingsChanged">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件设置 -->
        <el-tab-pane label="邮件设置" name="email">
          <el-form :model="emailSettings" label-width="120px">
            <el-form-item label="SMTP服务器">
              <el-input
                v-model="emailSettings.smtp_server"
                @input="markEmailSettingsChanged"
                placeholder="如: smtp.qq.com"
              />
            </el-form-item>

            <el-form-item label="SMTP端口">
              <el-input
                v-model="emailSettings.smtp_port"
                @input="markEmailSettingsChanged"
                placeholder="如: 587"
                type="number"
              />
            </el-form-item>

            <el-form-item label="SMTP用户名">
              <el-input
                v-model="emailSettings.smtp_username"
                @input="markEmailSettingsChanged"
                placeholder="邮箱账号"
              />
            </el-form-item>

            <el-form-item label="SMTP密码">
              <el-input
                v-model="emailSettings.smtp_password"
                @input="markEmailSettingsChanged"
                placeholder="邮箱密码或授权码"
                type="password"
                show-password
              />
            </el-form-item>

            <el-form-item label="使用TLS">
              <el-switch
                v-model="emailSettings.smtp_use_tls"
                @change="markEmailSettingsChanged"
              />
            </el-form-item>

            <el-form-item label="发件人邮箱">
              <el-input
                v-model="emailSettings.from_email"
                @input="markEmailSettingsChanged"
                placeholder="发件人邮箱地址"
              />
            </el-form-item>

            <el-form-item label="发件人名称">
              <el-input
                v-model="emailSettings.from_name"
                @input="markEmailSettingsChanged"
                placeholder="发件人显示名称"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="saveEmailSettings"
                :disabled="!emailSettingsChanged"
                :loading="savingEmail"
              >
                保存邮件设置
              </el-button>
              <el-button @click="resetEmailSettings" :disabled="!emailSettingsChanged">
                重置
              </el-button>
              <el-button type="success" @click="sendTestEmail" style="margin-left: 10px;">
                发送测试邮件
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件模板 -->
        <el-tab-pane label="邮件模板" name="templates">
          <el-button type="primary" @click="showTemplateDialog = true" style="margin-bottom: 20px;">
            新增模板
          </el-button>

          <el-table :data="emailTemplates" style="width: 100%">
            <el-table-column prop="name" label="模板名称" width="200" />
            <el-table-column prop="subject" label="邮件主题" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="is_active" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                  {{ scope.row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="editTemplate(scope.row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteTemplate(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 邮件日志 -->
        <el-tab-pane label="邮件日志" name="logs">
          <el-table :data="emailLogs" style="width: 100%">
            <el-table-column prop="to_email" label="收件人" width="200" />
            <el-table-column prop="subject" label="主题" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="发送时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="error_message" label="错误信息" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 邮件模板编辑对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      :title="editingTemplate ? '编辑邮件模板' : '新增邮件模板'"
      width="60%"
    >
      <el-form :model="templateForm" label-width="100px">
        <el-form-item label="模板名称" required>
          <el-input v-model="templateForm.name" :disabled="editingTemplate" />
        </el-form-item>
        <el-form-item label="邮件主题" required>
          <el-input v-model="templateForm.subject" />
        </el-form-item>
        <el-form-item label="邮件内容" required>
          <el-input
            v-model="templateForm.content"
            type="textarea"
            :rows="10"
            placeholder="支持HTML格式和模板变量，如 {{ username }}"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="templateForm.description" />
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="templateForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate">保存</el-button>
      </template>
    </el-dialog>

    <!-- 测试邮件对话框 -->
    <el-dialog v-model="showTestEmailDialog" title="发送测试邮件" width="40%">
      <el-form :model="testEmailForm" label-width="100px">
        <el-form-item label="收件人" required>
          <el-input v-model="testEmailForm.to_email" placeholder="请输入收件人邮箱" />
        </el-form-item>
        <el-form-item label="邮件主题" required>
          <el-input v-model="testEmailForm.subject" />
        </el-form-item>
        <el-form-item label="邮件内容" required>
          <el-input
            v-model="testEmailForm.content"
            type="textarea"
            :rows="5"
            placeholder="测试邮件内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTestEmailDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSendTestEmail">发送</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../api/index'

const activeTab = ref('basic')
const showTemplateDialog = ref(false)
const showTestEmailDialog = ref(false)
const editingTemplate = ref(false)

// 保存状态
const basicSettingsChanged = ref(false)
const emailSettingsChanged = ref(false)
const savingBasic = ref(false)
const savingEmail = ref(false)

// 原始设置备份
const originalBasicSettings = ref({})
const originalEmailSettings = ref({})

// 基础设置
const basicSettings = reactive({
  registration_enabled: true,
  site_name: '',
  site_url: ''
})

// 邮件设置
const emailSettings = reactive({
  smtp_server: '',
  smtp_port: '',
  smtp_username: '',
  smtp_password: '',
  smtp_use_tls: true,
  from_email: '',
  from_name: ''
})

// 邮件模板
const emailTemplates = ref([])
const templateForm = reactive({
  name: '',
  subject: '',
  content: '',
  description: '',
  is_active: true
})

// 邮件日志
const emailLogs = ref([])

// 测试邮件表单
const testEmailForm = reactive({
  to_email: '',
  subject: '测试邮件',
  content: '这是一封测试邮件，用于验证邮件配置是否正确。'
})

// 获取系统设置
const loadSystemSettings = async () => {
  try {
    const response = await api.get('/api/settings/system')
    const settings = response.data

    // 确保settings是数组
    if (Array.isArray(settings)) {
      settings.forEach(setting => {
        if (setting.key in basicSettings) {
          basicSettings[setting.key] = setting.key === 'registration_enabled'
            ? setting.value === 'true'
            : setting.value
        }
        if (setting.key in emailSettings) {
          emailSettings[setting.key] = setting.key === 'smtp_use_tls'
            ? setting.value === 'true'
            : setting.value
        }
      })
    } else {
      console.warn('Settings data is not an array:', settings)
    }

    // 保存原始设置
    originalBasicSettings.value = { ...basicSettings }
    originalEmailSettings.value = { ...emailSettings }

    // 重置变更状态
    basicSettingsChanged.value = false
    emailSettingsChanged.value = false
  } catch (error) {
    console.error('Load settings error:', error)
    ElMessage.error('加载系统设置失败')
  }
}

// 标记基础设置已变更
const markSettingsChanged = () => {
  basicSettingsChanged.value = true
}

// 标记邮件设置已变更
const markEmailSettingsChanged = () => {
  emailSettingsChanged.value = true
}

// 保存基础设置
const saveBasicSettings = async () => {
  savingBasic.value = true
  try {
    const settingsToSave = {}

    // 只保存变更的设置
    Object.keys(basicSettings).forEach(key => {
      const currentValue = typeof basicSettings[key] === 'boolean'
        ? basicSettings[key].toString()
        : (basicSettings[key] || '').toString()
      const originalValue = typeof originalBasicSettings.value[key] === 'boolean'
        ? originalBasicSettings.value[key].toString()
        : (originalBasicSettings.value[key] || '').toString()

      if (currentValue !== originalValue) {
        settingsToSave[key] = currentValue
      }
    })

    console.log('Settings to save:', settingsToSave) // 调试日志

    if (Object.keys(settingsToSave).length > 0) {
      await api.put('/api/settings/system/batch', settingsToSave)
      ElMessage.success('基础设置保存成功')

      // 更新原始设置
      originalBasicSettings.value = { ...basicSettings }
      basicSettingsChanged.value = false
    } else {
      ElMessage.info('没有设置需要保存')
    }
  } catch (error) {
    console.error('Save settings error:', error) // 调试日志
    ElMessage.error('保存基础设置失败')
  } finally {
    savingBasic.value = false
  }
}

// 重置基础设置
const resetBasicSettings = () => {
  Object.assign(basicSettings, originalBasicSettings.value)
  basicSettingsChanged.value = false
}

// 保存邮件设置
const saveEmailSettings = async () => {
  savingEmail.value = true
  try {
    const settingsToSave = {}

    // 只保存变更的设置
    Object.keys(emailSettings).forEach(key => {
      const currentValue = typeof emailSettings[key] === 'boolean'
        ? emailSettings[key].toString()
        : emailSettings[key]
      const originalValue = typeof originalEmailSettings.value[key] === 'boolean'
        ? originalEmailSettings.value[key].toString()
        : originalEmailSettings.value[key]

      if (currentValue !== originalValue) {
        settingsToSave[key] = currentValue
      }
    })

    if (Object.keys(settingsToSave).length > 0) {
      await api.put('/api/settings/system/batch', settingsToSave)
      ElMessage.success('邮件设置保存成功')

      // 更新原始设置
      originalEmailSettings.value = { ...emailSettings }
      emailSettingsChanged.value = false
    } else {
      ElMessage.info('没有设置需要保存')
    }
  } catch (error) {
    ElMessage.error('保存邮件设置失败')
  } finally {
    savingEmail.value = false
  }
}

// 重置邮件设置
const resetEmailSettings = () => {
  Object.assign(emailSettings, originalEmailSettings.value)
  emailSettingsChanged.value = false
}



// 获取邮件模板
const loadEmailTemplates = async () => {
  try {
    const response = await api.get('/api/settings/email-templates')
    emailTemplates.value = response.data
  } catch (error) {
    ElMessage.error('加载邮件模板失败')
  }
}

// 获取邮件日志
const loadEmailLogs = async () => {
  try {
    const response = await api.get('/api/settings/email-logs')
    emailLogs.value = response.data
  } catch (error) {
    ElMessage.error('加载邮件日志失败')
  }
}

// 编辑模板
const editTemplate = (template) => {
  editingTemplate.value = true
  Object.assign(templateForm, template)
  showTemplateDialog.value = true
}

// 保存模板
const saveTemplate = async () => {
  try {
    if (editingTemplate.value) {
      await api.put(`/api/settings/email-templates/${templateForm.id}`, templateForm)
    } else {
      await api.post('/api/settings/email-templates', templateForm)
    }

    ElMessage.success('模板保存成功')
    showTemplateDialog.value = false
    resetTemplateForm()
    loadEmailTemplates()
  } catch (error) {
    ElMessage.error('保存模板失败')
  }
}

// 删除模板
const deleteTemplate = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个邮件模板吗？', '确认删除', {
      type: 'warning'
    })

    await api.delete(`/api/settings/email-templates/${id}`)
    ElMessage.success('模板删除成功')
    loadEmailTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模板失败')
    }
  }
}

// 重置模板表单
const resetTemplateForm = () => {
  Object.assign(templateForm, {
    name: '',
    subject: '',
    content: '',
    description: '',
    is_active: true
  })
  editingTemplate.value = false
}

// 发送测试邮件
const sendTestEmail = () => {
  showTestEmailDialog.value = true
}

const confirmSendTestEmail = async () => {
  try {
    await api.post('/api/settings/send-test-email', testEmailForm)
    ElMessage.success('测试邮件发送成功')
    showTestEmailDialog.value = false
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    'sent': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    'sent': '已发送',
    'failed': '发送失败',
    'pending': '待发送'
  }
  return texts[status] || status
}

onMounted(() => {
  loadSystemSettings()
  loadEmailTemplates()
  loadEmailLogs()
})
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
